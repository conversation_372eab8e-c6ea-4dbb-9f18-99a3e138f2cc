#!/bin/bash

# 最终验证脚本 - 确保CLion配置完全正确

echo "🔍 CuraEngine CLion配置最终验证"
echo "=================================="

# 检查构建目录
echo "1. 检查构建目录..."
if [ -d "build/Debug" ] && [ -d "build/Release" ]; then
    echo "✅ 构建目录存在"
else
    echo "❌ 构建目录缺失"
    exit 1
fi

# 检查工具链文件
echo "2. 检查Conan工具链文件..."
if [ -f "build/Debug/build/Debug/generators/conan_toolchain.cmake" ] && [ -f "build/Release/build/Release/generators/conan_toolchain.cmake" ]; then
    echo "✅ 工具链文件存在"
else
    echo "❌ 工具链文件缺失"
    exit 1
fi

# 检查可执行文件
echo "3. 检查可执行文件..."
if [ -f "build/Debug/CuraEngine" ] && [ -f "build/Release/CuraEngine" ]; then
    echo "✅ 可执行文件存在"
else
    echo "❌ 可执行文件缺失"
    exit 1
fi

# 测试可执行文件
echo "4. 测试可执行文件..."
cd build/Debug
if ./CuraEngine help > /dev/null 2>&1; then
    echo "✅ Debug可执行文件运行正常"
else
    echo "❌ Debug可执行文件运行失败"
    exit 1
fi
cd ../Release
if ./CuraEngine help > /dev/null 2>&1; then
    echo "✅ Release可执行文件运行正常"
else
    echo "❌ Release可执行文件运行失败"
    exit 1
fi
cd ../..

# 检查CLion配置文件
echo "5. 检查CLion配置文件..."
if [ -f ".idea/cmake.xml" ]; then
    echo "✅ CMake配置文件存在"
    
    # 检查配置内容
    if grep -q "conan-debug" .idea/cmake.xml && grep -q "conan-release" .idea/cmake.xml; then
        echo "✅ CMake配置包含正确的构建配置"
    else
        echo "❌ CMake配置不完整"
        exit 1
    fi
else
    echo "❌ CMake配置文件不存在"
    exit 1
fi

if [ -f ".idea/runConfigurations/CuraEngine_Debug.xml" ] && [ -f ".idea/runConfigurations/CuraEngine_Release.xml" ]; then
    echo "✅ 运行配置文件存在"
else
    echo "❌ 运行配置文件缺失"
    exit 1
fi

# 检查工具链文件路径
echo "6. 验证工具链文件路径..."
debug_toolchain="build/Debug/build/Debug/generators/conan_toolchain.cmake"
release_toolchain="build/Release/build/Release/generators/conan_toolchain.cmake"

if grep -q "$debug_toolchain" .idea/cmake.xml && grep -q "$release_toolchain" .idea/cmake.xml; then
    echo "✅ 工具链文件路径正确"
else
    echo "❌ 工具链文件路径不正确"
    exit 1
fi

echo ""
echo "🎉 所有验证通过！CLion配置完全正确！"
echo ""
echo "📋 下一步操作："
echo "1. 在CLion中打开项目"
echo "2. 点击 Tools → CMake → Reset Cache and Reload Project"
echo "3. 选择 'conan-debug' 或 'conan-release' 配置"
echo "4. 点击构建按钮或使用快捷键 Cmd+F9"
echo "5. 使用 'CuraEngine Debug' 运行配置进行调试"
echo ""
echo "🔧 如果遇到问题："
echo "- 运行 ./fix_clion_issues.sh 进行故障排除"
echo "- 重新启动CLion"
echo "- 检查 CLion_Setup_Guide.md 获取详细说明"
echo ""
echo "✨ 配置完成！现在可以开始CuraEngine的二次开发了！"
