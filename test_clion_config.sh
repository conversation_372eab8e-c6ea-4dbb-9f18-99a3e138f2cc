#!/bin/bash

# 测试CLion配置的脚本

echo "=== 测试CLion配置 ==="

# 检查构建目录
echo "1. 检查构建目录..."
if [ -d "build/Debug" ]; then
    echo "✅ Debug构建目录存在"
else
    echo "❌ Debug构建目录不存在"
    exit 1
fi

if [ -d "build/Release" ]; then
    echo "✅ Release构建目录存在"
else
    echo "❌ Release构建目录不存在"
    exit 1
fi

# 检查工具链文件
echo "2. 检查Conan工具链文件..."
if [ -f "build/Debug/build/Debug/generators/conan_toolchain.cmake" ]; then
    echo "✅ Debug工具链文件存在"
else
    echo "❌ Debug工具链文件不存在"
    exit 1
fi

if [ -f "build/Release/build/Release/generators/conan_toolchain.cmake" ]; then
    echo "✅ Release工具链文件存在"
else
    echo "❌ Release工具链文件不存在"
    exit 1
fi

# 检查可执行文件
echo "3. 检查可执行文件..."
if [ -f "build/Debug/CuraEngine" ]; then
    echo "✅ Debug可执行文件存在"
else
    echo "❌ Debug可执行文件不存在"
    exit 1
fi

if [ -f "build/Release/CuraEngine" ]; then
    echo "✅ Release可执行文件存在"
else
    echo "❌ Release可执行文件不存在"
    exit 1
fi

# 测试可执行文件
echo "4. 测试可执行文件..."
cd build/Debug
if ./CuraEngine help > /dev/null 2>&1; then
    echo "✅ Debug可执行文件运行正常"
else
    echo "❌ Debug可执行文件运行失败"
    exit 1
fi
cd ../..

cd build/Release
if ./CuraEngine help > /dev/null 2>&1; then
    echo "✅ Release可执行文件运行正常"
else
    echo "❌ Release可执行文件运行失败"
    exit 1
fi
cd ../..

# 检查CLion配置文件
echo "5. 检查CLion配置文件..."
if [ -f ".idea/cmake.xml" ]; then
    echo "✅ CMake配置文件存在"
else
    echo "❌ CMake配置文件不存在"
    exit 1
fi

if [ -f ".idea/runConfigurations/CuraEngine_Debug.xml" ]; then
    echo "✅ Debug运行配置存在"
else
    echo "❌ Debug运行配置不存在"
    exit 1
fi

if [ -f ".idea/runConfigurations/CuraEngine_Release.xml" ]; then
    echo "✅ Release运行配置存在"
else
    echo "❌ Release运行配置不存在"
    exit 1
fi

echo ""
echo "🎉 所有检查通过！CLion配置正确。"
echo ""
echo "下一步操作："
echo "1. 在CLion中打开项目"
echo "2. 选择 'conan-debug' 或 'conan-release' 配置"
echo "3. 点击构建按钮或使用快捷键 Cmd+F9"
echo "4. 使用 'CuraEngine Debug' 运行配置进行调试"
echo ""
echo "如果CLion中看不到配置，请："
echo "- 点击 Tools → CMake → Reset Cache and Reload Project"
echo "- 或重新启动CLion"
