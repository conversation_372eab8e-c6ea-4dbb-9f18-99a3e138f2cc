#!/bin/bash

# CLion问题修复脚本

echo "=== CLion问题修复脚本 ==="

# 选项菜单
echo "请选择要执行的操作："
echo "1. 重新构建Debug版本"
echo "2. 重新构建Release版本"
echo "3. 重新构建所有版本"
echo "4. 清理并重新构建"
echo "5. 修复CMake配置文件"
echo "6. 测试当前配置"
echo "7. 退出"

read -p "请输入选项 (1-7): " choice

case $choice in
    1)
        echo "重新构建Debug版本..."
        rm -rf build/Debug
        ./build_debug.sh
        ;;
    2)
        echo "重新构建Release版本..."
        rm -rf build/Release
        ./build_release.sh
        ;;
    3)
        echo "重新构建所有版本..."
        rm -rf build/Debug build/Release
        ./build_debug.sh
        ./build_release.sh
        ;;
    4)
        echo "清理并重新构建..."
        rm -rf build/ cmake-build-*
        ./build_debug.sh
        ./build_release.sh
        ;;
    5)
        echo "修复CMake配置文件..."
        # 重新生成cmake.xml
        cat > .idea/cmake.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CMakeSettings">
    <configurations>
      <configuration PROFILE_NAME="conan-debug" ENABLED="true" CONFIG_NAME="Debug" TOOLCHAIN_NAME="Default" 
                     GENERATION_DIR="$PROJECT_DIR$/build/Debug" 
                     BUILD_OPTIONS="-j 14" 
                     CMAKE_ARGS="-DCMAKE_TOOLCHAIN_FILE=$PROJECT_DIR$/build/Debug/build/Debug/generators/conan_toolchain.cmake;-DCMAKE_BUILD_TYPE=Debug;-DENABLE_TESTING=OFF;-DENABLE_BENCHMARKS=OFF;-DENABLE_ARCUS=ON;-DENABLE_PLUGINS=ON" 
                     GENERATION_OPTIONS="-G Ninja" />
      <configuration PROFILE_NAME="conan-release" ENABLED="true" CONFIG_NAME="Release" TOOLCHAIN_NAME="Default" 
                     GENERATION_DIR="$PROJECT_DIR$/build/Release" 
                     BUILD_OPTIONS="-j 14" 
                     CMAKE_ARGS="-DCMAKE_TOOLCHAIN_FILE=$PROJECT_DIR$/build/Release/build/Release/generators/conan_toolchain.cmake;-DCMAKE_BUILD_TYPE=Release;-DENABLE_TESTING=OFF;-DENABLE_BENCHMARKS=OFF;-DENABLE_ARCUS=ON;-DENABLE_PLUGINS=ON" 
                     GENERATION_OPTIONS="-G Ninja" />
    </configurations>
  </component>
</project>
EOF
        echo "✅ CMake配置文件已修复"
        ;;
    6)
        echo "测试当前配置..."
        ./test_clion_config.sh
        ;;
    7)
        echo "退出"
        exit 0
        ;;
    *)
        echo "无效选项"
        exit 1
        ;;
esac

echo ""
echo "操作完成！"
echo ""
echo "CLion使用提示："
echo "1. 在CLion中点击 Tools → CMake → Reset Cache and Reload Project"
echo "2. 选择 'conan-debug' 或 'conan-release' 配置"
echo "3. 如果仍有问题，请重新启动CLion"
